/// Member Profile Provider - GymKod Pro Mobile
///
/// Bu provider üye profil yönetimi için state management sağlar.
/// Referans: Angular frontend'deki member profile component'i
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';
import '../../data/services/member_api_service.dart';
import '../../domain/models/member_models.dart';

/// Member Profile State
class MemberProfileState {
  final MemberProfileDto? profile;
  final bool isLoading;
  final bool isUpdating;
  final String? error;
  final String? successMessage;
  final DateTime? lastFetchTime;
  final bool isInitialized;

  const MemberProfileState({
    this.profile,
    this.isLoading = false,
    this.isUpdating = false,
    this.error,
    this.successMessage,
    this.lastFetchTime,
    this.isInitialized = false,
  });

  MemberProfileState copyWith({
    MemberProfileDto? profile,
    bool? isLoading,
    bool? isUpdating,
    String? error,
    String? successMessage,
    DateTime? lastFetchTime,
    bool? isInitialized,
    bool clearError = false,
    bool clearSuccessMessage = false,
    bool clearProfile = false,
  }) {
    return MemberProfileState(
      profile: clearProfile ? null : (profile ?? this.profile),
      isLoading: isLoading ?? this.isLoading,
      isUpdating: isUpdating ?? this.isUpdating,
      error: clearError ? null : (error ?? this.error),
      successMessage: clearSuccessMessage ? null : (successMessage ?? this.successMessage),
      lastFetchTime: lastFetchTime ?? this.lastFetchTime,
      isInitialized: isInitialized ?? this.isInitialized,
    );
  }

  /// Cache geçerli mi? (5 dakika)
  bool get isCacheValid {
    if (lastFetchTime == null || profile == null) return false;
    final now = DateTime.now();
    final cacheAge = now.difference(lastFetchTime!);
    return cacheAge.inMinutes < 5; // 5 dakika cache
  }

  @override
  String toString() {
    return 'MemberProfileState(profile: $profile, isLoading: $isLoading, isUpdating: $isUpdating, error: $error, successMessage: $successMessage, lastFetchTime: $lastFetchTime, isInitialized: $isInitialized)';
  }
}

/// Member Profile Provider
class MemberProfileNotifier extends StateNotifier<MemberProfileState> {
  final MemberRepository _memberRepository;

  MemberProfileNotifier(this._memberRepository) : super(const MemberProfileState());

  /// Profil bilgilerini yükle (cache-aware)
  Future<void> loadProfile({bool forceRefresh = false}) async {
    try {
      // Cache kontrolü - force refresh değilse ve cache geçerliyse API çağrısı yapma
      if (!forceRefresh && state.isCacheValid) {
        LoggingService.authLog('Member profile loaded from cache');
        return;
      }

      LoggingService.authLog('Loading member profile from API');

      state = state.copyWith(
        isLoading: true,
        clearError: true,
        clearSuccessMessage: true,
      );

      final result = await _memberRepository.getMemberProfile();

      if (result.isSuccess && result.data != null) {
        LoggingService.authLog('Member profile loaded successfully',
          details: 'User: ${result.data!.firstName} ${result.data!.lastName}');

        state = state.copyWith(
          profile: result.data,
          isLoading: false,
          lastFetchTime: DateTime.now(),
          isInitialized: true,
        );
      } else {
        LoggingService.authLog('Failed to load member profile', details: result.message);
        
        state = state.copyWith(
          isLoading: false,
          error: result.message,
        );
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'MemberProfileNotifier loadProfile');
      
      state = state.copyWith(
        isLoading: false,
        error: 'Profil bilgileri yüklenirken bir hata oluştu.',
      );
    }
  }

  /// Profil bilgilerini güncelle
  Future<bool> updateProfile(MemberProfileUpdateDto updateDto) async {
    try {
      LoggingService.authLog('Updating member profile',
        details: 'User: ${updateDto.firstName} ${updateDto.lastName}');
      
      state = state.copyWith(
        isUpdating: true,
        clearError: true,
        clearSuccessMessage: true,
      );

      final result = await _memberRepository.updateMemberProfile(updateDto);

      if (result.isSuccess) {
        LoggingService.authLog('Member profile updated successfully');

        // Profili yeniden yükle (force refresh)
        await loadProfile(forceRefresh: true);

        state = state.copyWith(
          isUpdating: false,
          successMessage: result.message,
        );

        return true;
      } else {
        LoggingService.authLog('Failed to update member profile', details: result.message);
        
        state = state.copyWith(
          isUpdating: false,
          error: result.message,
        );

        return false;
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'MemberProfileNotifier updateProfile');
      
      state = state.copyWith(
        isUpdating: false,
        error: 'Profil güncellenirken bir hata oluştu.',
      );

      return false;
    }
  }

  /// Hata mesajını temizle
  void clearError() {
    state = state.copyWith(clearError: true);
  }

  /// Başarı mesajını temizle
  void clearSuccessMessage() {
    state = state.copyWith(clearSuccessMessage: true);
  }

  /// Profili temizle (logout durumunda)
  void clearProfile() {
    state = const MemberProfileState();
  }

  /// Profil yenileme (force refresh)
  Future<void> refreshProfile() async {
    await loadProfile(forceRefresh: true);
  }
}

/// Member Profile Provider
final memberProfileProvider = StateNotifierProvider<MemberProfileNotifier, MemberProfileState>((ref) {
  final memberRepository = ref.read(memberRepositoryProvider);
  return MemberProfileNotifier(memberRepository);
});

/// Profil yüklü mü kontrolü
final isProfileLoadedProvider = Provider<bool>((ref) {
  final profileState = ref.watch(memberProfileProvider);
  return profileState.profile != null;
});

/// Profil yükleniyor mu kontrolü
final isProfileLoadingProvider = Provider<bool>((ref) {
  final profileState = ref.watch(memberProfileProvider);
  return profileState.isLoading;
});

/// Profil güncelleniyor mu kontrolü
final isProfileUpdatingProvider = Provider<bool>((ref) {
  final profileState = ref.watch(memberProfileProvider);
  return profileState.isUpdating;
});

/// Cache geçerli mi kontrolü
final isProfileCacheValidProvider = Provider<bool>((ref) {
  final profileState = ref.watch(memberProfileProvider);
  return profileState.isCacheValid;
});

/// Profil initialize edilmiş mi kontrolü
final isProfileInitializedProvider = Provider<bool>((ref) {
  final profileState = ref.watch(memberProfileProvider);
  return profileState.isInitialized;
});
