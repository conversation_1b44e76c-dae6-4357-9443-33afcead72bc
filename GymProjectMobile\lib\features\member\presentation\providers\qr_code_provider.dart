/// QR Code Provider - GymKod Pro Mobile
///
/// Bu provider Angular frontend'deki my-qr component'inden uyarlanmıştır.
/// Referans: GymProjectFrontend/src/app/components/my-qr/my-qr.component.ts
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';
import '../../../auth/presentation/providers/auth_provider.dart';
import '../../domain/models/member_models.dart';
import '../../data/services/member_api_service.dart';
import '../../data/services/qr_timer_service.dart';

/// QR Code State Notifier
/// Angular frontend'deki QR component state management'ını Flutter'a uyarlar
class QRCodeNotifier extends StateNotifier<QRCodeState> {
  final MemberRepository _memberRepository;
  final QRTimerService _qrTimerService;
  final Ref _ref;

  QRCodeNotifier(this._memberRepository, this._qrTimerService, this._ref)
      : super(QRCodeState.initial()) {
    // QR kod süresi bitince otomatik yenileme callback'ini ayarla (Angular pattern)
    _qrTimerService.setOnExpiredCallback(() {
      _autoRefreshQRCode();
    });

    // Auth state değişikliklerini dinle - logout olduğunda QR kodunu temizle
    _ref.listen<AuthState>(authProvider, (previous, next) {
      if (previous?.isAuthenticated == true && next.isAuthenticated == false) {
        LoggingService.stateLog('QRCode', 'User logged out - clearing QR code');
        clearQRCode();
      }
    });
  }

  /// QR kodunu yükle (Angular: loadMemberQR)
  Future<void> loadQRCode() async {
    try {
      LoggingService.stateLog('QRCode', 'Loading QR code');

      // Auth durumunu kontrol et
      final authState = _ref.read(authProvider);
      if (!authState.isAuthenticated) {
        LoggingService.stateLog('QRCode', 'QR code load blocked - user not authenticated');
        return;
      }

      // Loading durumundaysa tekrar yükleme
      if (state.isLoading) {
        LoggingService.stateLog('QRCode', 'QR code already loading');
        return;
      }

      state = state.copyWith(isLoading: true, clearError: true);

      // Rate limiting kontrolü (3 dakikada 1 istek)
      if (!state.canRefresh) {
        LoggingService.stateLog('QRCode', 'Rate limit active');
        state = state.copyWith(
          isLoading: false,
          error: 'Çok fazla istek gönderildi. 3 dakika sonra tekrar deneyin.',
        );
        return;
      }

      final result = await _memberRepository.getMemberQR();

      if (result.isSuccess && result.data != null) {
        final qrData = result.data!;

        LoggingService.stateLog('QRCode', 'QR code loaded successfully',
          state: 'Name: ${qrData.name}, Phone: ${qrData.phoneNumber}');

        state = state.copyWith(
          qrData: qrData,
          isLoading: false,
          clearError: true,
        );

        // Timer'ı başlat (Angular frontend pattern)
        _startQRTimer();

        // Rate limiting (3 dakika)
        _setRateLimit();

      } else {
        LoggingService.stateLog('QRCode', 'QR code load failed', state: result.message);

        state = state.copyWith(
          isLoading: false,
          error: result.message,
        );
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'QRCodeNotifier loadQRCode');

      state = state.copyWith(
        isLoading: false,
        error: 'QR kod yüklenemedi. Lütfen tekrar deneyin.',
      );
    }
  }

  /// QR kodunu yenile (Angular: refreshQR)
  Future<void> refreshQRCode() async {
    try {
      LoggingService.stateLog('QRCode', 'Refreshing QR code');

      // Timer'ı durdur
      _qrTimerService.resetTimer(_ref);

      // QR kodunu tekrar yükle
      await loadQRCode();

    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'QRCodeNotifier refreshQRCode');

      state = state.copyWith(
        isLoading: false,
        error: 'QR kod yenilenemedi. Lütfen tekrar deneyin.',
      );
    }
  }

  /// QR kodunu otomatik yenile (Angular: auto refresh pattern)
  Future<void> _autoRefreshQRCode() async {
    try {
      LoggingService.stateLog('QRCode', 'Auto refreshing QR code - timer expired');

      // Auth durumunu kontrol et - logout olmuşsa refresh yapma
      final authState = _ref.read(authProvider);
      if (!authState.isAuthenticated) {
        LoggingService.stateLog('QRCode', 'Auto refresh blocked - user not authenticated');
        clearQRCode(); // QR kodunu temizle
        return;
      }

      // Rate limit kontrolü - otomatik yenileme için daha esnek
      if (!state.canRefresh) {
        LoggingService.stateLog('QRCode', 'Auto refresh blocked by rate limit');
        return;
      }

      // Loading durumundaysa yenileme yapma
      if (state.isLoading) {
        LoggingService.stateLog('QRCode', 'Auto refresh blocked - already loading');
        return;
      }

      // QR kodunu otomatik yenile
      await loadQRCode();

    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'QRCodeNotifier _autoRefreshQRCode');
    }
  }

  /// QR timer'ını başlat (Angular: startQRCodeTimer)
  void _startQRTimer() {
    try {
      LoggingService.stateLog('QRCode', 'Starting QR timer (5 minutes)');

      _qrTimerService.startTimer(_ref);

    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'QRCodeNotifier _startQRTimer');
    }
  }

  /// Rate limiting ayarla (3 dakika)
  void _setRateLimit() {
    try {
      state = state.copyWith(canRefresh: false);

      // 3 dakika sonra rate limit'i kaldır
      Future.delayed(const Duration(minutes: 3), () {
        if (mounted) {
          state = state.copyWith(canRefresh: true);
          LoggingService.stateLog('QRCode', 'Rate limit removed');
        }
      });

      LoggingService.stateLog('QRCode', 'Rate limit set for 3 minutes');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'QRCodeNotifier _setRateLimit');
    }
  }

  /// Error'ı temizle
  void clearError() {
    state = state.copyWith(clearError: true);
  }

  /// QR kodunu temizle
  void clearQRCode() {
    try {
      _qrTimerService.resetTimer(_ref);
      state = QRCodeState.initial();
      LoggingService.stateLog('QRCode', 'QR code cleared');
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'QRCodeNotifier clearQRCode');
    }
  }

  @override
  void dispose() {
    _qrTimerService.dispose();
    super.dispose();
  }
}

/// QR Code Provider
final qrCodeProvider = StateNotifierProvider<QRCodeNotifier, QRCodeState>((ref) {
  final memberRepository = ref.read(memberRepositoryProvider);
  final qrTimerService = ref.read(qrTimerServiceProvider);
  return QRCodeNotifier(memberRepository, qrTimerService, ref);
});

/// QR Code Getters
final qrCodeDataProvider = Provider<GetMemberQRByPhoneNumberDto?>((ref) {
  final qrState = ref.watch(qrCodeProvider);
  return qrState.qrData;
});

final qrCodeIsLoadingProvider = Provider<bool>((ref) {
  final qrState = ref.watch(qrCodeProvider);
  return qrState.isLoading;
});

final qrCodeErrorProvider = Provider<String?>((ref) {
  final qrState = ref.watch(qrCodeProvider);
  return qrState.error;
});

final qrCodeCanShowProvider = Provider<bool>((ref) {
  final qrState = ref.watch(qrCodeProvider);
  return qrState.canShowQR;
});

final qrCodeCanRefreshProvider = Provider<bool>((ref) {
  final qrState = ref.watch(qrCodeProvider);
  return qrState.canRefresh;
});

final qrCodeIsActiveProvider = Provider<bool>((ref) {
  final qrState = ref.watch(qrCodeProvider);
  return qrState.isQRActive;
});
